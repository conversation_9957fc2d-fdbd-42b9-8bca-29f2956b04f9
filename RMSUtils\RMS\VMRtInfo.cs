using RMSBLL.RMS;
using RMSModel.Config;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSUtils.RMS
{
    public class VMRtInfo
    {

        public VMRtInfo()
        {
            // rtListall =MRtInfoBll.GRtInfoAll();
            rtListall = VMPC.apidata.rtListall;
        }
        /// <summary>
        ///所有房型信息
        /// </summary>
        public List<MRt_MShop> rtListall { get; set; }
        /// <summary>
        /// 指定门店房型信息
        /// </summary>
        public List<MRt_MShop> rtList { get; set; }
       
        /// <summary>
        /// 门店ID
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 获取指定门店房型信息
        /// </summary>
        /// <param name="shopid"></param>
        public List<MRt_MShop> GetRtInfo(int shopid)
        {
            this.ShopId = shopid;
            if (rtListall != null)
            {
                rtList = rtListall.FindAll(i => i.ShopId == shopid);
            }
            return rtList;
        }

        /// <summary>
        /// 房型预约数绑定
        /// </summary>
        public void SetRtBookNumber(string TimeNo, List<MBookNumber> bookNumberList)
        {
            if (bookNumberList != null)
            { 
                foreach (var item in rtList)
                { 
                    MBookNumber  booknumber = bookNumberList.Find(i => i.TineNo == TimeNo && i.RtNo == item.RtNo);
                    if (booknumber == null)
                    {
                        booknumber = new MBookNumber();
                    }
                    item.mBookNumber = booknumber;
                }
            }

        }


        /// <summary>
        /// 根据人数获取指定房型
        /// </summary>
        /// <param name="number">人数</param>
        public MRt_MShop GetBookNumberRt(int number)
        {
            MRt_MShop rtinfo = null;
            var result = rtList.Where(i => i.NumberMin <= number && i.NumberMax >= number).OrderBy(i=>i.NumberMax).ToList();
            foreach (var item in result)
            {
                if (item.mBookNumber != null && item.mBookNumber.BookSurplus > 0)
                {
                    rtinfo = item;
                    break;
                }
            }
            return rtinfo;
        }


    }
}
