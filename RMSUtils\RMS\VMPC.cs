﻿using Newtonsoft.Json;
using RMSBLL.RMS;

using RMSModel.API;
using RMSModel.Config;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
using RMSModel.TCP;
using RMSUtils.TCP;
using RMSUtils.WebApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Script.Serialization;
namespace RMSUtils.RMS
{
    public class VMPC : RMSBaseModel
    {
        public int OnNumber { get { return _OnNumber; } set { _OnNumber = value; ReisePropertyChanged("OnNumber"); } }
        private int _OnNumber { get; set; }

        public static bool IsPrint = true;
        /// <summary>
        /// 是否打印食品卡头
        /// </summary>
        public static bool IsPrintTitle = false;
        /// <summary>
        /// 系统时间
        /// </summary>
        public VMTimeDate vmTimeDate { get; set; }
        /// <summary>
        /// 分店数据列表
        /// </summary>
        public VMShopInfo vmShopInfo { get; set; }
        /// <summary>
        /// 时段信息
        /// </summary>
        public VMShopTimeInfo vmShopTimeInfo { get; set; }
        /// <summary>
        /// 房型信息
        /// </summary>
        public VMRtInfo vmRtInfo { get; set; }
        /// <summary>
        /// 顾客信息
        /// </summary>
        public VMCustInfo vmCustInfo { get; set; }
        /// <summary>
        /// 消费类型
        /// </summary>
        public VMConTypeInfo vmConTypeInfo { get; set; }
        /// <summary>
        /// 房间信息
        /// </summary>
        public VMRmInfo vmrmInfo { get; set; }
        /// <summary>
        /// 固定配置文件（包含门店基本信息）
        /// </summary>
        public CFixedConfig CfixedConfig { get; set; }
        /// <summary>
        /// 员工操作
        /// </summary>
        public VMUserInfo vmuserinfo { get; set; }
        /// <summary>
        /// 门店基本配置信息
        /// </summary>
        public VMWorkConfig vmworkconfig { get; set; }
        /// <summary>
        /// 预订数量
        /// </summary>
        public VMBookNumber vmbooknumber { get; set; }
        public ListenPing listenping { get; set; }
        public static int ShopId { get; set; }
        /// <summary>
        /// 客户端
        /// </summary>
        public Client client { get; set; }
        /// <summary>
        /// 账单状态
        /// </summary>
        public VMBillStatus vmBillStatus { get; set; }
        /// <summary>
        /// 微信信息
        /// </summary>
        public WechatUser wechatuser { get; set; }
        /// <summary>
        /// 房型价格
        /// </summary>
        public VMRtprice vmrtprice { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public List<RMSTime> timelist { get; set; }
        public EditionInfo edition { get; set; }
        public static ApiData apidata = null;
        public static JavaScriptSerializer js = new JavaScriptSerializer();
        /// <summary>
        /// 来电号码
        /// </summary>
        public static string CallTel = string.Empty;

        /// <summary>
        /// 创建默认API数据
        /// </summary>
        private void CreateDefaultApiData()
        {
            try
            {
                apidata = new ApiData();

                // 创建默认门店列表
                apidata.shopList = new List<MShopInfo>
                {
                    new MShopInfo { ShopId = 0, ShopName = "预约中心" },
                    new MShopInfo { ShopId = 1, ShopName = "测试门店1" }
                };

                // 创建默认工作列表
                apidata.workList = new List<MWorkNotShop_Shop>();

                // 创建默认时段列表
                apidata.timeListall = new List<MShopTimeInfoJoin>();

                // 创建默认房型列表
                apidata.rtListall = new List<MRt_MShop>();

                // 创建默认房间列表
                apidata.rmlistall = new List<RMSModel.RMS.Api.MRmInfoApi>();

                // 创建默认消费类型列表
                apidata.listConTypeall = new List<MConTypeInfo>();

                // 创建默认账单状态列表
                apidata.billStatusList = new List<MBillStatus>();

                // 设置当前日期
                apidata.nowDate = DateTime.Now;

                System.Diagnostics.Debug.WriteLine("已创建默认API数据");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建默认API数据失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 设置程序初始化
        /// </summary>
        public void SetInit()
        {
            try
            {
                CfixedConfig = new CFixedConfig() { ShopId = int.Parse(Regedit.GetVal("ShopId")) };

                // 尝试从API获取基础信息
                try
                {
                    apidata = RMSBLLApi.RMS.BookInfoApiBll.GetBookInfo(CfixedConfig.ShopId);
                }
                catch (Exception apiEx)
                {
                    System.Diagnostics.Debug.WriteLine($"API调用失败: {apiEx.Message}");
                    // API失败时创建默认数据
                    CreateDefaultApiData();
                }

                // 确保apidata不为空
                if (apidata == null)
                {
                    CreateDefaultApiData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"VMPC初始化失败: {ex.Message}");
                // 使用默认配置
                CfixedConfig = new CFixedConfig() { ShopId = 1 };
                CreateDefaultApiData();
            }
            #region 基础信息获取-无需访问sql
            edition = new EditionInfo();//获取版本信息
            timelist = RMSTime.GetTime(VMTimeDate.GetWordDate(), 7);//获取预约日期信息
            vmTimeDate = new VMTimeDate();
            vmCustInfo = new VMCustInfo();



            #endregion

            vmworkconfig = new VMWorkConfig();
            vmShopInfo = new VMShopInfo();
            vmConTypeInfo = new VMConTypeInfo();
            vmrtprice = new VMRtprice(this);
            if (CfixedConfig.ShopId != 0)
            {
                CfixedConfig.shopInfo = VMShopInfo.shopList.Find(i => i.ShopId == CfixedConfig.ShopId);//根据配置的ShopId，获取指定的ShopInfo
            }
            if (CfixedConfig.shopInfo == null)
            {
                //找不到门店信息
            }
            vmShopTimeInfo = new VMShopTimeInfo();
            vmRtInfo = new VMRtInfo();
            vmrmInfo = new VMRmInfo();
            vmbooknumber = new VMBookNumber();
            client = new Client();
            client.Connect("193.112.2.229", 12323);
            vmuserinfo = new VMUserInfo(CfixedConfig.ShopId, client);
            ShopId = CfixedConfig.ShopId;
            vmBillStatus = new VMBillStatus();
            wechatuser = new WechatUser();
            PrintAttribute printattr = VMPC.apidata.printattr;
            if (printattr != null)
            {
                IsPrintTitle = printattr.FrontPrint;
            }
        }


        /// <summary>
        /// 设置程序初始化-API
        /// </summary>
        public void SetInitAPI()
        {
            vmbooknumber = new VMBookNumber(true);
            vmShopTimeInfo = new VMShopTimeInfo(true);
            vmworkconfig = new VMWorkConfig(true);
            client = new Client();
            vmrmInfo = new VMRmInfo(true);
            vmCustInfo = new VMCustInfo();
            vmuserinfo = new VMUserInfo(0, client);
            vmuserinfo.muserinfo = new MUserInfo() { ShopId = 0, UserId = "000001", UserName = "微信预约" };
            //client.Connect("193.112.2.229", 12323);


        }
        ///// <summary>
        ///// 获取指定日期时段预约数量
        /////   1：在缓存获取预订数量，当没有缓存则初始化新增并保存，如果有缓存则直接返回
        ///// </summary>
        ///// <param name="ShopId">门店</param>
        ///// <param name="ComDate">预约日期：yyyyMMdd</param>
        ///// <param name="timeList">时段集合</param>
        //public List<MBookNumber> GetBookNumber(int ShopId, string ComDate, List<MShopTimeInfoJoin> timeList)
        //{
        //    List<MBookNumber> bookNumberList = null;
        //    string key = ComDate + ShopId;
        //    RmsResult rmsReuslt = vmbooknumber.GetBookNumber(ComDate,ShopId);
        //    if (rmsReuslt != null)
        //    {
        //        if (rmsReuslt.Data == null || rmsReuslt.Data.Count==0)
        //        {
        //            vmrmInfo.GetRoomInfo(ShopId);
        //            bookNumberList = vmrmInfo.GetRoomNumber(ComDate, timeList);
        //            vmbooknumber.SetBookNumberUpdate(key, bookNumberList);
        //        }
        //        else
        //        {
        //            bookNumberList = rmsReuslt.Data;
        //        }
        //    }

        //    vmShopTimeInfo.SetTimeBookNumber(bookNumberList);
        //    return bookNumberList;
        //}

        /// <summary>
        /// 获取指定日期时段预约数量
        ///   1：在缓存获取预订数量，当没有缓存则初始化新增并保存，如果有缓存则直接返回
        /// </summary>
        /// <param name="ShopId">门店</param>
        /// <param name="ComDate">预约日期：yyyyMMdd</param>
        /// <param name="timeList">时段集合</param>
        public List<MBookNumber> GetBookNumber(int ShopId, string ComDate, List<MShopTimeInfoJoin> timeList)
        {
            List<MBookNumber> bookNumberList = null;
            bookNumberList = vmbooknumber.GetBookNumber(ComDate, ShopId, vmrmInfo.GetRoomNumber(ShopId, ComDate, timeList));
            vmShopTimeInfo.SetTimeBookNumber(bookNumberList);
            return bookNumberList;
        }

        /// <summary>
        /// 新增预约记录
        ///      1：当顾客首次预订没有记录时，则新增一条记录（CustKey传递null则默认当作是用户自定义生成）
        /// </summary>
        /// <param name="custinfo">顾客信息</param>
        /// <param name="bookinfo">预约信息</param>
        public void SetCustBook(MCustInfo custinfo, MBookCacheInfo bookinfo)
        {
            //前置数据校验
            MRt_MShop rtshop = vmRtInfo.rtList.Find(i => i.RtNo == bookinfo.RtNo);
            if (rtshop == null)
                throw new Exception("未找到匹配房型！");
            //if (rtshop.mBookNumber.BookSurplus - 1 < 0)
            //    throw new Exception(rtshop.RtName + "已满!");

            //房间数足够才进行下面的操作
            rtshop.mBookNumber.BookSurplus--;

            vmuserinfo.SetCustBook(custinfo, bookinfo);
            vmuserinfo.GetBookCount();//增加统计
            
            vmShopTimeInfo.SetTimeBookNumber(vmbooknumber.bookNumberList);
            //vmbooknumber.SetBookNumberUpdate(bookinfo.ComeDate + bookinfo.ShopId, vmbooknumber.bookNumberList);
            //if (rtshop.mBookNumber.BookSurplus <= 0)
            //{
            //    Console.WriteLine(rtshop.RtName + "已满!");
            //}
        }

        /// <summary>
        /// 修改预约记录
        ///      1：当预订信息Ikey等于空则视作新增预订，否则视为修改
        /// </summary>
        /// <param name="custinfo">顾客信息</param>
        /// <param name="bookinfo">预约信息</param>
        public void SetCustBookUpdate(MCustInfo custinfo, MBookCacheInfo bookinfo, MBookCacheInfo bookinfoOld)
        {
            bookinfo.Ikey = bookinfoOld.Ikey;
            bookinfo.BookShopId = bookinfoOld.BookShopId;
            bookinfo.CustKey = bookinfoOld.CustKey;
            bookinfo.CustName = custinfo.CustName + custinfo.CustSex;
            bookinfo.CustTel = bookinfoOld.CustTel;
            bookinfo.BookUserId = bookinfoOld.BookUserId;
            bookinfo.BookUserName = bookinfoOld.BookUserName;
            bookinfo.BookDateTime = bookinfoOld.BookDateTime;
            bool isNewBook = false;
            if (bookinfo.ComeDate != bookinfoOld.ComeDate || bookinfo.ShopId != bookinfoOld.ShopId)
            {//跨日期门店修改则重新修改预约号
                bookinfo.BookNo = vmuserinfo.vmbookNo.BookNo;
                isNewBook = true;
            }
            else
            {
                bookinfo.BookNo = bookinfoOld.BookNo;
            }
            SetCustBook(custinfo, bookinfo);
            if (isNewBook == true)
            {
                vmuserinfo.vmbookNo.NGetNewBookNo(bookinfo.ShopId, bookinfo.ComeDate);//预约成功后重新获取预约号   
            }

        }

    }
}
