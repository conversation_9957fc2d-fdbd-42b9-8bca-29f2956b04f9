using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using TH_DAO;

namespace RMSDao
{
    public static class DbHelp_rms2019<T>
    {
        public static DAO<T> dao = new DAO<T>();
        /// <summary>
        /// 构造器
        ///    初始化Mysql 链接字符
        /// </summary>
        static DbHelp_rms2019()
        {
            string rmsServerName = "192.168.2.14";
            // 尝试从配置读取
            string configValue = ConfigurationManager.AppSettings["RmsServerName"];
            if (!string.IsNullOrEmpty(configValue))
            {
                rmsServerName = configValue;
            }

            // 构建连接字符串
            dao.connstr = $"Data Source={rmsServerName};Initial Catalog=rms2019;User ID=sa;Password=***";
        }
    }
}
