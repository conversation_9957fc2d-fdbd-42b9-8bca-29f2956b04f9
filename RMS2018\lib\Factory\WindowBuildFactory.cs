using RMS2018.ViewModel;
using RMSModel;
using RMSModel.RMS;
using RMSModel.TCP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Script.Serialization;
using System.Windows;

namespace RMS2018.lib.Factory
{
    /// <summary>
    /// 窗口创建工厂
    /// </summary>
    public static class WindowBuildFactory
    {
        static WindowBuildFactory() {
            sing = SingleRun.GetSingle();
            viewModel = BuildFactory.CreateMainViewModel();
            sing.ViewMode = viewModel;
        }
        static JavaScriptSerializer js = new JavaScriptSerializer();

        static WOpen wopen;
        static WBook wbook;
        static WSMS wsms;
        static SingleRun sing;
        static MainViewModel viewModel;
        /// <summary>
        ///  创建窗口（未创建则新增，已创建则Show）
        /// </summary>
        /// <param name="ui"></param>
        public static Window CreateWindow(EWindowUi? ui)
        {
            Window win = null;         
            var vmpc = sing.vmpc;
            if (vmpc == null) return null;

            //初始化消息窗口
            if (wsms == null)
            {
                wsms = new WSMS();
               // wsms.DataContext = viewModel;
                vmpc.client.callbackAll = callbackall;
                sing.wsms = wsms;

            }
            ///没有传递指定类型，则根据门店自行判断
            sing.vmpc.CfixedConfig.ShopId = 11; // 强制设置为11用于测试
            if (ui.HasValue == false)
            {
                ui = sing.vmpc.CfixedConfig.ShopId == 0 ? RMSModel.EWindowUi.预约 : RMSModel.EWindowUi.开房;
            }
            ///根据类型创建窗口
            switch (ui)
            {
                case EWindowUi.开房:
                    if (vmpc.CfixedConfig != null && vmpc.CfixedConfig.ShopId == 0)
                    {
                        MessageBox.Show("当前门店无法执行此操作！");
                        break;
                    }
                    if (wopen == null)
                    {
                        wopen = new WOpen();
                        sing.wopen = wopen;
                       // wopen.DataContext = viewModel;
                    }
                    wopen.WindowState = WindowState.Maximized;
                    wopen.WindowStyle = WindowStyle.None;
                    wopen.Show();
                    win = wopen;
                    break;
                case EWindowUi.预约:
                    if (wbook == null)
                    {
                        wbook = new WBook();
                        sing.wbook = wbook;
                      //  wbook.DataContext = viewModel;
                    }
                    wbook.WindowState = WindowState.Maximized;
                    wbook.WindowStyle = WindowStyle.None;
                    wbook.Show();
                    win = wbook;
                    break;
                case EWindowUi.信息:

                    wsms.usms.tbConent.Focus();
                    wsms.usms.Visibility = Visibility.Visible;
                    wsms.ShowDialog();
                    win = wsms;
                    break;
            }
            return win;
        }


        /// <summary>
        /// 全局回调函数
        /// </summary>
        /// <param name="model"></param>
        public static void callbackall(TcpReponse model)
        {
            wsms.Dispatcher.Invoke(new Action(() =>
            {
                switch (model.Request)
                {
                    case 1:
                        MSMS _sms = js.ConvertToType<MSMS>(model.Data);
                        if (_sms != null)
                        {
                            wsms.SetCallback(_sms);
                            if (_sms.ToShopId == -1 || _sms.FromShopId == wsms.usms.shopid || _sms.ToShopId == wsms.usms.shopid)
                            {
                                if (wbook != null)
                                    wbook.SetNewMsg(_sms);
                                if (wopen != null)
                                    wopen.SetNewMsg(_sms);
                            }
                        }

                        break;
                    case 2:
                        if (wopen != null)
                        {
                            wopen.SetCallback(model);
                        }
                        break;
                    case 3:
                        MSMS sms = js.ConvertToType<MSMS>(model.Data);
                        WCall call = new WCall(sms.Message);
                        break;
                    case 6: // 强制顾客离线
                        if (sing.vmpc.vmuserinfo.muserinfo.UserId == model.Conent)
                        {
                            MessageBox.Show("您当前使用帐号所关联的企业微信，强制要求退出登录，确定后将会退出程序！");
                            Environment.Exit(0);
                        }
                        break;
                    case 7: // 发送在线用户数量
                        try
                        {
                            sing.vmpc.OnNumber = int.Parse(model.Conent);
                        }
                        catch
                        {
                        }
                        break;
                }
            }));
        }



    }
}
