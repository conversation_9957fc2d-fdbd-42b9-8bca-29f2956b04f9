using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using RMSModel.ExtensionRMS;
using System.Data;
using RMSBLL.RMS;
using RMSModel.RMS;
using RMSBLL.DbFood;
using RMSModel.Config;

namespace RMS2018.Controls
{
    /// <summary>
    /// RoomChange.xaml 的交互逻辑
    /// </summary>
    public partial class RoomChange : UserControl
    {
        List<MRm_Rt_MArea_MShop> list = new List<MRm_Rt_MArea_MShop>();
        List<MRm_Rt_MArea_MShop> OutRms = new List<MRm_Rt_MArea_MShop>();
        List<MRm_Rt_MArea_MShop> ToRms = new List<MRm_Rt_MArea_MShop>();
        //List<ExchangeWait> List_EW = new List<ExchangeWait>();
        List<RmExchange> List_EW1 = new List<RmExchange>();
        List<RmExchange> List_RE = new List<RmExchange>();
        DataTable dt = new DataTable();
        //经手人，即操作员工
        string Username = "";
        /// <summary>
        /// 登录门店ID
        /// </summary>
        int shopid;
        public RoomChange()
        {
            try
            {
                InitializeComponent();
                this.shopid = CFixedConfig.shopInfoStatic.ShopId;
                //this.shopid = 4;
                Username = SingleRun.GetSingle().vmpc.vmuserinfo.muserinfo.UserName;
                //Username = "测试用户";
            }
            catch
            {
            }
            SetData();
        }
        public void SetData(string ParmRoom)
        {
            Data();
            if (ParmRoom != "" && ParmRoom != null)
            {
                if (OutRms.Select(i => i.RmNo).ToList().Contains(ParmRoom))
                {
                    Com_OutRoom.Text = ParmRoom;
                }
                if (ToRms.Select(i => i.RmNo).ToList().Contains(ParmRoom))
                {
                    Com_ChangeToRoom.Text = ParmRoom;
                }
            }
        }

        public void SetData()
        {
            Data();
        }

        public void Data()
        {
            try
            {
                list = MRmInfoBll.GetMRmInfo(shopid).OrderBy(i => i.RmNo).ThenBy(i => i.AreaId).ToList();
                //Grid_CRoomsRecord.ItemsSource = List_RE = RmExchangeBll.GetData(shopid).OrderByDescending(i => i.ExchangeDate).Take(50).ToList();
                //Grid_CRooms.ItemsSource = List_EW = ExchangeWaitBll.GetData(shopid);
                //List<string> listOut = List_EW.Select(t => t.FromRmNo).ToList();

                //2020-12-09 jjy 修改，减少服务器的访问量和数据的查询量以提交数据访问的速度,发生死锁时写入日志
                List<RmExchange> alllist = null;
                try
                {
                     alllist = RmExchangeBll.GetData1(shopid);
                }
                catch (System.Data.SqlClient.SqlException e)
                {
                    if (e.Number == 1205)
                    {
                        Log.WriteLog("在执行‘GetExchangeData’过程获取转发记录和预转记录时发生锁死");
                    }
                    else if (e.Number == 1222 || e.Number == 5245)
                    {
                        Log.WriteLog("在执行‘GetExchangeData’过程获取转发记录和预转记录时发生超时");
                    }
                    else
                    {
                        throw;
                    }
                }
                if (alllist != null && alllist.Count>0) 
                {
                    List_RE = alllist.Where(i => i.IsExchange == true).ToList();
                    List_EW1 = alllist.Where(i => i.IsExchange == false).ToList();
                    Grid_CRoomsRecord.ItemsSource = List_RE;
                    Grid_CRooms.ItemsSource = List_EW1;
                    List<string> listOut = List_EW1.Select(t => t.FromRmNo).ToList();

                   // Com_OutRoom.ItemsSource = OutRms = list.FindAll(i => (i.RmsStatus == "C" || i.RmsStatus == "U" || i.RmsStatus == "W") && (!listOut.Contains(i.RmNo))).ToList();
                    //2020-12-18 jjy 修改，预结状态也可以转房
                    Com_OutRoom.ItemsSource = OutRms = list.FindAll(i => (i.RmsStatus == "C" || i.RmsStatus == "U" || i.RmsStatus == "W" || i.RmsStatus == "F") && (!listOut.Contains(i.RmNo))).ToList();
                    Com_ChangeToRoom.ItemsSource = ToRms = list.FindAll(i => i.RmsStatus == "E").ToList();
                    if (Com_OutRoom.Items.Count > 0)
                    {
                        Com_OutRoom.SelectedIndex = 0;
                    }
                    if (Com_ChangeToRoom.Items.Count > 0)
                    {
                        Com_ChangeToRoom.SelectedIndex = 0;
                    }
                }
                
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 预转把选择的房号添加到下表中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Prerotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Com_OutRoom.SelectedItem != null && Com_ChangeToRoom.SelectedItem != null)
                {
                    //转出房号这一列的内容集合
                    //List<string> listOut = List_EW.Select(t => t.FromRmNo).ToList();
                    //List<string> listTo = List_EW.Select(t => t.ToRmNo).ToList();
                    //2020-12-09 jjy 修改
                    List<string> listOut = List_EW1.Select(t => t.FromRmNo).ToList();
                    List<string> listTo = List_EW1.Select(t => t.ToRmNo).ToList();
                    if (listOut.Contains(Com_OutRoom.Text) || listTo.Contains(Com_ChangeToRoom.Text))
                    {
                        MessageBox.Show("下表已经预转过该房间");
                    }
                    else
                    {
                        ExchangeWait EW = new ExchangeWait();
                        EW.FromRmNo = Com_OutRoom.Text;
                        EW.ToRmNo = Com_ChangeToRoom.Text;
                        EW.UserName = Username;
                        EW.ShopId = shopid;
                        try
                        {
                            int count = ExchangeWaitBll.DataAdd(EW);
                            if (count > 0)
                            {
                                MRmInfoBll.GetMRmInfo_Udp(shopid, EW.ToRmNo, "H", "", "");
                                SetData();
                                //MessageBox.Show("已添加" + count + "记录");
                            }
                            else
                            {
                                MessageBox.Show("添加失败");
                            }
                        }
                        catch (Exception)
                        {
                            MessageBox.Show("添加失败");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 取消转房
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Cancel_Room_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Grid_CRooms.SelectedIndex > -1)
                {
                    ExchangeWait EW = Grid_CRooms.SelectedItem as ExchangeWait;
                    ExchangeWaitBll.DataDel(EW.FromRmNo, EW.ShopId);
                    MRmInfoBll.GetMRmInfo_Udp(shopid, EW.ToRmNo, "E");
                    SetData();
                }
                else
                {
                    MessageBox.Show("请选中上表的一行");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 根据两个下拉框所选房号转房
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_CRoom_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                #region 开始转房
                if (Com_OutRoom.SelectedItem != null && Com_ChangeToRoom.SelectedItem != null)
                {
                    ExchangeWait EW = new ExchangeWait();
                    EW.FromRmNo = Com_OutRoom.Text;
                    EW.ToRmNo = Com_ChangeToRoom.Text;
                    EW.UserName = Username;
                    EW.ShopId = shopid;
                    MessageBoxResult result = MessageBox.Show(string.Format("确定把'{0}'转入'{1}'？", EW.FromRmNo, EW.ToRmNo), "提示", MessageBoxButton.OKCancel, MessageBoxImage.Information);
                    if (result == MessageBoxResult.OK)
                    {
                        try
                        {
                            SingleRun.GetSingle().vmpc.vmuserinfo.SetMvodRoomModel(shopid, EW.FromRmNo, string.Empty);
                            SingleRun.GetSingle().vmpc.vmuserinfo.SetMvodRoomModel(shopid, EW.ToRmNo, "group");
                        }
                        catch
                        {
                        }
                        RmExchangeAdd(EW);
                        SetData();
                        Com_OutRoom.SelectedItem = OutRms.Find(i => i.RmNo == EW.ToRmNo);
                        MessageBox.Show("转房成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }//2020-09-18 jjy 修改转房按钮，无需预转，可以直接转房
                else if (Com_OutRoom.SelectedItem == null)
                {
                    MessageBox.Show("转出房号不得为空，转房失败！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else if (Com_ChangeToRoom.SelectedItem == null)
                {
                    MessageBox.Show("转入房号不得为空，转房失败！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                #endregion

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 根据下表所选的选项进行转房
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_CgRoom_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Grid_CRooms.SelectedIndex > -1)
                {
                    ExchangeWait EW = Grid_CRooms.SelectedItem as ExchangeWait;
                    MessageBoxResult result = MessageBox.Show(string.Format("确定把'{0}'转入'{1}'？", EW.FromRmNo, EW.ToRmNo), "提示", MessageBoxButton.OKCancel, MessageBoxImage.Information);
                    if (result == MessageBoxResult.OK)
                    {
                        RmExchangeAdd(EW);
                        SetData();
                        Com_OutRoom.SelectedItem = OutRms.Find(i => i.RmNo == EW.ToRmNo);
                        MessageBox.Show("转房成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);

                    }
                }
                else
                {
                    MessageBox.Show("请选中上表的一行");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 把预转房间转入转房记录,并刷新数据
        /// </summary>
        /// <param name="EW"></param>
        public void RmExchangeAdd(ExchangeWait EW)
        {
            try
            {
                //RmExchange RE = new RmExchange();
                //RE.ExchangeDate = DateTime.Now;
                //RE.FromRmNo = EW.FromRmNo;
                //RE.ToRmNo = EW.ToRmNo;
                //RE.UserName = EW.UserName;
                //RE.ShopId = shopid;
              //  RmExchangeBll.DataAdd(RE);
                //分别根据门店和房号查出转入房间和转出房间的信息
                MRm_Rt_MArea_MShop FromRmInfo = MRmInfoBll.GetMRmInfoByRmNo(EW.FromRmNo, shopid);
             //   MRm_Rt_MArea_MShop ToRmInfo = MRmInfoBll.GetMRmInfoByRmNo(EW.ToRmNo, shopid);
                //通过RoomToIkey修改开房（房型名称、房型编号\房号（转入房号））
               // OpenCacheInfoBll.SetUpdateDataByRoomToKey(FromRmInfo.RoomToIkey, ToRmInfo.RtName, ToRmInfo.RtNo, ToRmInfo.RmNo, EW.FromRmNo);
                if (FromRmInfo.RmsStatus == "H")
                {
                    FromRmInfo.RmsStatus = "E";
                    MessageBox.Show("检测到转出房状态为：H" + EW.FromRmNo + shopid);
                }
                try
                {
                    if (FromRmInfo.RmsStatus != "W")
                    {
                        th_rms2019Bll.Rm_Exchange(EW.FromRmNo, EW.ToRmNo);
                        //2020-11-10 jjy 添加写入日志
                        Log.WriteLog(EW.FromRmNo + "房转房操作，将房间转至" + EW.ToRmNo);
                    }
                }
                catch (Exception)
                {
                }
                ////转出转入房间信息互换
                 //MRmInfoBll.GetMRmInfo_Udp(shopid, EW.ToRmNo, FromRmInfo.RtUp, FromRmInfo.RoomToIkey, FromRmInfo.BillTot, FromRmInfo.BookNo, FromRmInfo.CustTel, FromRmInfo.IsDiscount, FromRmInfo.RmsStatus);
                //MRmInfoBll.GetMRmInfo_Udp(shopid, EW.FromRmNo, ToRmInfo.RtUp, ToRmInfo.RoomToIkey, ToRmInfo.BillTot, ToRmInfo.BookNo, ToRmInfo.CustTel, ToRmInfo.IsDiscount, "E");

                ////删除预转记录
                //ExchangeWaitBll.DataDel(EW.FromRmNo, EW.ShopId);
                //2020-12-10 jjy 修改 转出转入房间信息互换，发生死锁时写入日志
                try
                {
                    // 根据UI选择决定调用哪个存储过程
                    if (rbUpdateRate.IsChecked == true) // 如果选择"付费升级"
                    {
                        // 调用原有的、全功能更新的存储过程
                        MRmInfoBll.GetMRmInfo_Up(shopid, EW.ToRmNo, EW.FromRmNo, EW.UserName);
                        Log.WriteLog(EW.FromRmNo + "房转房操作成功（付费升级）");
                    }
                    else // 如果选择"免费升级"
                    {
                        // 调用新的、只换房号不改价格的存储过程
                        MRmInfoBll.GetMRmInfo_Up_KeepRate(shopid, EW.ToRmNo, EW.FromRmNo, EW.UserName);
                        Log.WriteLog(EW.FromRmNo + "房转房操作成功（免费升级）");
                    }
                }
                catch (System.Data.SqlClient.SqlException e)
                {
                    if (e.Number == 1205)
                    {
                        Log.WriteLog("在执行‘GetMRmInfo_Udp’过程互换房间信息时发生锁死");
                    }
                    else if (e.Number == 1222 || e.Number == 5245) 
                    {
                        Log.WriteLog("在执行‘GetMRmInfo_Udp’过程互换房间信息时发生超时");
                    }
                    else
                    {
                        throw;
                    }
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("转房失败: " + ex.Message);
                Log.WriteLog("转房失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 把表中预转的房间全部转出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_CgAllRoom_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBoxResult result = MessageBox.Show("确定列表选项全部转房？", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Information);
                if (result == MessageBoxResult.OK)
                {
                    string LastToRmNo = "";
                    foreach (ExchangeWait EW in Grid_CRooms.Items)
                    {
                        RmExchangeAdd(EW);
                        LastToRmNo = EW.ToRmNo;
                    }
                    SetData();
                    if (LastToRmNo != "")
                    {
                        Com_OutRoom.SelectedItem = OutRms.Find(i => i.RmNo == LastToRmNo); MessageBox.Show("全部转出成功！");
                    }
                    else
                    {
                        MessageBox.Show("无可转房，转房失败！");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 关闭界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            this.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 刷新房间
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Refresh_Click(object sender, RoutedEventArgs e)
        {
            Button CkBtn = sender as Button;
            CkBtn.IsEnabled = false;
            //DispatcherHelper.DoEvents(),让按钮灰化得以立刻执行，否则禁用按钮是要在整个事件结束后才会响应
            DispatcherHelper.DoEvents();
            SetData();
            CkBtn.IsEnabled = true;
            MessageBox.Show("刷新成功");
        }

    }
}
