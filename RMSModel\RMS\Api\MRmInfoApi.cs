using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSModel.RMS.Api
{
    public class MRmInfoApi
    {
        /// <summary> 
        /// 分店ID
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 房型
        /// </summary>
        public string RtNo { get; set; }
        /// <summary>
        /// 房间号
        /// </summary>
        public string RmNo { get; set; }
        /// <summary>
        /// 房型名称
        /// </summary>
        public string RtName { get; set; }
        /// <summary>
        /// 房间状态 (E:空闲, U:占用, D:清扫, B:坏房)
        /// </summary>
        public string RmsStatus { get; set; }
        /// <summary>
        /// 房间占用关联ID
        /// </summary>
        public string RoomToIkey { get; set; }
       
      
    }
}
