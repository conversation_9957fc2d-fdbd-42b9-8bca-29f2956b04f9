﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using RMSBLL.RMS;
using RMSModel.RMS;
using System.Windows.Navigation;
using System.Reflection;
using RMSUtils.WebApi;
using System.Threading;
using mshtml;
using System.Net;
using RMSBLL.RMS.UserBll;
using RMSModel.RMS.User;
using RMS2018.lib.Factory;
using RMS2018.ComponentWpf;
using System.Configuration;
using RMSModel.Config;

namespace RMS2018
{
    /// <summary>
    /// WLogin.xaml 的交互逻辑
    /// </summary>
    public partial class WLogin : Window
    {
        SingleRun sing;

        public object LuserinfoBll { get; private set; }

        public WLogin()
        {
            InitializeComponent();
            sing = SingleRun.GetSingle();
            sing.InitSingle();

           // web.Navigate(new Uri("http://www.tang-hui.com.cn/rms2019_web/notice.aspx"));
            //web.Navigate(new Uri("https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=ww3302cb3e0acccc6e&agentid=1000036&redirect_uri=http://yy.tang-hui.com.cn/Code.html&state=&login_type=jssdk"));
            //web.Navigating += Web_Navigating;

           
        }
        /// <summary>
        ///  页面内容变更检测获取相关信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void Web_Navigating(object sender, NavigatingCancelEventArgs e)
        {

            var fiComWebBrowser = typeof(WebBrowser).GetField("_axIWebBrowser2", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            if (fiComWebBrowser == null)
                return;

            object objComWebBrowser = fiComWebBrowser.GetValue(web);
            if (objComWebBrowser == null)
                return;

            objComWebBrowser.GetType().InvokeMember("Silent", System.Reflection.BindingFlags.SetProperty, null, objComWebBrowser, new object[] { true });

            try
            {
                string Code = string.Empty;
                string[] para = e.Uri.Query.Replace("?", "").Split('&');
                foreach (var item in para)
                {
                    string[] itemAttr = item.Split('=');
                    if (itemAttr != null && itemAttr[0] == "code")
                    {
                        Code = itemAttr[1];
                    }
                }
                string webaccess_token = Json.post(string.Format("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=ww3302cb3e0acccc6e&corpsecret=DADKGM575COPP6IvYu2-miG13BKo82nFok9ZFZdBTxw"));
                WeChatToken wechatToken = Json.GetData<WeChatToken>(webaccess_token);
                string str = Json.post(string.Format("https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={0}&code={1}", wechatToken.access_token, Code));
                WeChatUser webdata = Json.GetData<WeChatUser>(str);
                string userinfostring = Json.post(string.Format("http://hr.tang-hui.com.cn/services/userGet.php?id=1000004&userid={0}&callback=?", webdata.UserId));
                var json = (Newtonsoft.Json.Linq.JObject)Newtonsoft.Json.JsonConvert.DeserializeObject(userinfostring.Trim().TrimStart('?').TrimStart('(').TrimEnd(')'));
                var t = json["wechat"];
                List<WeChatUser> list = Newtonsoft.Json.JsonConvert.DeserializeObject<List<WeChatUser>>(t.ToString()) as List<WeChatUser>;
                WeChatUser userinfo = list.FirstOrDefault();
                if (userinfo != null)
                {
                    // gridload.Visibility = Visibility.Visible;
                    SetInitDataCheck(userinfo);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("登录过程发生异常：" + ex.Message);
            }

        }

        /// <summary>
        /// 检测数据是否已经完成初始化
        /// </summary>
        public void SetInitDataCheck(WeChatUser userinfo)
        {
            // 使用后台线程等待初始化完成
            Thread waitThread = new Thread(() =>
            {
                // 等待初始化完成
                while (sing == null || !sing.islood)
                {
                    Thread.Sleep(100);
                }

                // 确保CFixedConfig正确初始化
                EnsureConfigInitialized();

                // 在UI线程中执行后续操作
                this.Dispatcher.Invoke(new Action(() =>
                {
                    try
                    {
                        MUserInfo muserinfo = new MUserInfo();
                        muserinfo.UserId = userinfo.UserId;
                        muserinfo.UserName = userinfo.name;
                        muserinfo.avatar = userinfo.avatar;
                        sing.vmpc.vmuserinfo.muserinfo = muserinfo;

                        if (userinfo.UserId == null)
                        {
                            MessageBox.Show("登录信息获取失败，请重新登录！");
                            return;
                        }

                        // 跳过密码检查（测试模式）
                        bool isTestMode = ConfigurationManager.AppSettings["TestMode"] == "true";
                        if (!isTestMode)
                        {
                            UserInfo userinfocheck = UserInfoBll.GetUserInfoByUserId(userinfo.UserId);
                            if (userinfocheck != null && string.IsNullOrEmpty(userinfocheck.Pwd) == true)
                            {
                                WPasswordSet wpassword = new WPasswordSet(userinfo.UserId);
                                wpassword.ShowDialog();
                            }
                        }

                        sing.vmpc.client.SetLoginInfo_On(new RMSModel.RMS.User.UserInfo()
                        {
                            UserId = userinfo.UserId,
                            ShopId = sing.vmpc.CfixedConfig.ShopId.ToString(),
                            UserName = userinfo.name,
                            Avatar = userinfo.avatar,
                            Pc = Dns.GetHostName()
                        });

                        WindowBuildFactory.CreateWindow(null);
                        sing.SetThreadTile();
                        this.Close();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"初始化主界面时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }));
            });

            waitThread.IsBackground = true;
            waitThread.Start();
        }

        /// <summary>
        /// 确保配置正确初始化
        /// </summary>
        private void EnsureConfigInitialized()
        {
            try
            {
                if (sing?.vmpc?.CfixedConfig != null)
                {
                    // 确保shopInfoStatic被正确设置
                    if (CFixedConfig.shopInfoStatic == null && sing.vmpc.CfixedConfig.shopInfo != null)
                    {
                        CFixedConfig.shopInfoStatic = sing.vmpc.CfixedConfig.shopInfo;
                    }

                    // 如果仍然为空，创建一个默认的
                    if (CFixedConfig.shopInfoStatic == null)
                    {
                        int shopId = sing.vmpc.CfixedConfig.ShopId;
                        if (shopId == 0)
                        {
                            CFixedConfig.shopInfoStatic = new MShopInfo() { ShopName = "预约中心", ShopId = 0 };
                        }
                        else
                        {
                            // 尝试从配置获取shopId
                            string configShopId = ConfigurationManager.AppSettings["shopid"];
                            if (!string.IsNullOrEmpty(configShopId) && int.TryParse(configShopId, out int testShopId))
                            {
                                shopId = testShopId;
                            }

                            CFixedConfig.shopInfoStatic = new MShopInfo()
                            {
                                ShopName = $"测试门店{shopId}",
                                ShopId = shopId
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置初始化错误: {ex.Message}");
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {

        }

        private void But_Regist_Click_1(object sender, RoutedEventArgs e)
        {
            if (Txt_UserId.Text != null && Txt_UserId.Text != "" && Txt_UserPwd != null && Txt_UserPwd.Password != "")
            {
                MUserInfo userinfo = new MUserInfo();
                userinfo.UserId = Txt_UserId.Text;
                userinfo.UserPwd = Txt_UserPwd.Password;
                int result = SingleRun.GetSingle().vmpc.vmuserinfo.AddUserLogin(userinfo);
                if (result == 0)
                {
                    MessageBox.Show("注册成功");
                }
                if (result == 1)
                {
                    MessageBox.Show("该工号已被注册，请重新输入");
                    Txt_UserId.Text = "";
                    Txt_UserId.Focus();
                }

            }
        }
        public void SetLogin(Window window)
        {
            if (Txt_UserId.Text != null && Txt_UserId.Text != "" && Txt_UserPwd != null && Txt_UserPwd.Password != "")
            {
                MUserInfo userinfo = new MUserInfo();
                userinfo.UserId = Txt_UserId.Text;
                userinfo.UserPwd = Txt_UserPwd.Password;
                int result = SingleRun.GetSingle().vmpc.vmuserinfo.SetUserLogin(userinfo);
                if (result == 0)
                {
                    MessageBox.Show("您输入的工号不存在，请重新输入");
                    Txt_UserId.Text = "";
                    Txt_UserId.Focus();
                }
                if (result == 1)
                {
                    if (window != null)
                    {
                        window.Show();
                        window.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                        this.Close();
                    }
                }
                if (result == 2)
                {
                    MessageBox.Show("您输入的密码有误，请重新输入");
                    Txt_UserPwd.Password = "";
                    Txt_UserPwd.Focus();
                }

            }
        }

        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {
            SetLogin(new WOpen());
        }

        private void btnBook_Click(object sender, RoutedEventArgs e)
        {

            SetLogin(new WBook());
        }

        private void btnCode_Click(object sender, RoutedEventArgs e)
        {
            // WCall call = new WCall( "番禺店今日停订谢谢！");
            web.Refresh();
            WAndUserInfo anduser = new WAndUserInfo();
            anduser.Show();
        }


        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            tbUserId.Focus();
            //WpfFrame.Init();
        }
        /// <summary>
        /// 登录按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLogin_Click(object sender, RoutedEventArgs e)
        {

            UserInfo userinfo = UserInfoBll.GetUserInfoByUserId(tbUserId.Text);
            if (userinfo == null)
            {
                MessageBox.Show("帐号不存在，请核对输入是否错误/未激活!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                tbUserId.Focus();
                tbUserId.SelectAll();
            }
            else
            {
                if (string.IsNullOrEmpty(userinfo.Pwd) == true)
                {
                    // MessageBox.Show("您的帐号未设置密码，请扫码进行激活!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                    //2020-10-26 jjy修改弹窗提示
                    MessageBox.Show("您的帐号未设置密码，请前往修改密码处获取临时密码进行设置密码!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);

                }
                else if (userinfo.Pwd != tbUserPwd.Password)
                {
                    MessageBox.Show("密码错误，请核对输入信息!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                    tbUserPwd.Focus();
                    tbUserPwd.SelectAll();
                }
                else
                {
                    SetInitDataCheck(new WeChatUser() { avatar = userinfo.Avatar, name = userinfo.UserName, UserId = userinfo.UserId });
                }
            }
        }

        private void tbUserPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnLogin_Click(null, null);

            }
        }

        private void tbUserId_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.Key == Key.Enter)
                {
                    TraversalRequest request = new TraversalRequest(FocusNavigationDirection.Next);
                    UIElement focusElement = Keyboard.FocusedElement as UIElement;
                    if (focusElement != null)
                    {
                        focusElement.MoveFocus(request);
                    }
                    e.Handled = true;
                }
            }
            catch (Exception e1)
            {
                //ExceptionDialog.Show(e1);
            }
        }

        /// <summary>
        /// 2020-09-23 jjy 新增修改员工登录密码
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUpdatePassword_Click(object sender, RoutedEventArgs e)
        {
            if (tbUserId.Text.Trim() == "")
            {
                MessageBox.Show("请输入员工号进行密码修改！");
                return;
            }

            WModifyPassword modify = new WModifyPassword(tbUserId.Text.Trim());
            modify.ShowDialog();
        }


    }

}
