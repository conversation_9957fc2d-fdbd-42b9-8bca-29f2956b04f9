<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="RMS2018.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <connectionStrings>
    <add name="dbfoodConn" connectionString="Data Source=193.112.2.229;Initial Catalog=dbfood;User ID=sa;Password=Musicbox@***" />
    <!-- <add name="dbfoodConn" connectionString="Data Source=************;Initial Catalog=dbfood;User ID=sa;Password=***"/> -->
  </connectionStrings>
  <applicationSettings>
    <RMS2018.Properties.Settings>
      <setting name="RMS2018_RMSService_RMSBasicInfo" serializeAs="String">
        <value>http://rms.tang-hui.com.cn/WebService/RMSBasicInfo.asmx</value>
      </setting>
    </RMS2018.Properties.Settings>
  </applicationSettings>
  <appSettings>
    <add key="callServer" value="************"></add>
    <add key="RemoteHost" value="webbook"></add>
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="TestMode" value="true"/>
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IBook" />
        <binding name="BasicHttpBinding_ISaasPos" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://logic.tang-hui.com.cn:9102/RmsService" binding="basicHttpBinding"
          bindingConfiguration="BasicHttpBinding_IBook" contract="BookService.IBook"
          name="BasicHttpBinding_IBook" />
      <endpoint address="http://www.tang-hui.com.cn:9199/SaasPos/"  binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISaasPos" contract="SaasPosService.ISaasPos" name="BasicHttpBinding_ISaasPos" />
    </client>
  </system.serviceModel>
</configuration>