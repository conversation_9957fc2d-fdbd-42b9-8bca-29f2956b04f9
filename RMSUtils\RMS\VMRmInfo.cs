using RMSBLL.RMS;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
using RMSModel.RMS.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSUtils.RMS
{
    /// <summary>
    ///  房间信息
    /// </summary>
    public class VMRmInfo
    {

        public VMRmInfo()
        {
            // rmlistall= MRmInfoBll.GetMRmInfo()；
            rmlistall = VMPC.apidata.rmlistall;
        }
        public VMRmInfo(bool isapi)
        {
            rmlistall = MRmInfoBll.GetMRmInfo_Api();
           
        }
        /// <summary>
        /// 所有房间信息
        /// </summary>
        List<MRmInfoApi> rmlistall;
        /// <summary>
        /// 当前门店信息
        /// </summary>
        List<MRmInfoApi> rmlist;
        public int Shopid = 0;
        /// <summary>
        /// 获取房间信息
        /// </summary>
        public List<MRmInfoApi> GetRoomInfo(int Shopid)
        {
            this.Shopid = Shopid;
            if (rmlistall != null)
            {
                rmlist = rmlistall.FindAll(i => i.ShopId == Shopid);
            }
            return rmlist;
        }
        /// <summary>
        /// 获取当前门店的房间初始预订数量
        /// </summary>
        /// <returns></returns>
        public List<MBookNumber> GetRoomNumber(int shopid, string ComDate, List<MShopTimeInfoJoin> timeList)
        {
            List<MBookNumber> resultList = new List<MBookNumber>();
            foreach (var itemRow in timeList)
            {
                foreach (var item in rmlistall.FindAll(j => j.ShopId == shopid))
                {
                    MBookNumber listitem = resultList.Find(j => j.RtNo == item.RtNo && j.TineNo == itemRow.TimeNo);
                    if (listitem == null)
                    {
                        MBookNumber data = new MBookNumber();
                        data.RtNo = item.RtNo;
                        data.TineNo = itemRow.TimeNo;
                        listitem = data;
                        resultList.Add(data);
                    }
                    listitem.BookSurplus++;
                    listitem.BookNoSure++;
                }
                DateTime DateStart = DateTime.ParseExact(ComDate + itemRow.BegTime.ToString().PadLeft(4, '0'), "yyyyMMddHHmm", System.Globalization.CultureInfo.CurrentCulture);
                DateTime DateEnd = DateTime.ParseExact(ComDate + itemRow.EndTime.ToString().PadLeft(4, '0'), "yyyyMMddHHmm", System.Globalization.CultureInfo.CurrentCulture);
                if (DateEnd < DateStart)
                    DateEnd.AddDays(1);
                List<MWorkBookOut> bookoutlist = MWorkBookOutBll.GetBookOut_Now(shopid, DateStart, DateEnd);

                if (bookoutlist != null)
                {
                    foreach (var bookout in bookoutlist)
                    {
                        MBookNumber booknumber = resultList.Find(j => j.RtNo == bookout.RtNo && j.TineNo == itemRow.TimeNo);
                        if (booknumber != null)
                        {
                            booknumber.BookNoSure += bookout.Number;
                            booknumber.BookSurplus += bookout.Number;
                        }
                    }

                }

            }

            return resultList;
        }
    }
}
